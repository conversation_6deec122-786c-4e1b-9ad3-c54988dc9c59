{"name": "tayduong-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.453.0", "next": "14.2.16", "next-intl": "^3.23.5", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "tayduong-fe": "file:"}, "devDependencies": {"@types/node": "^20.17.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "eslint": "^8.57.1", "eslint-config-next": "14.2.16", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.6.3"}}