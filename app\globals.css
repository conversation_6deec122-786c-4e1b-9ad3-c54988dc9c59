@tailwind base;
@tailwind components;
@tailwind utilities;

body {
    font-family: var(--font-roboto), <PERSON><PERSON>, sans-serif;
}

/* Vietnamese text optimization */
.font-roboto {
    font-family: var(--font-roboto), Roboto, sans-serif;
    font-feature-settings: "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }

    /* Enhanced Typography Scale */
    .text-display-xl {
        font-size: 4.5rem;
        line-height: 1.1;
        font-weight: 700;
        letter-spacing: -0.025em;
    }
    .text-display-lg {
        font-size: 3.75rem;
        line-height: 1.1;
        font-weight: 700;
        letter-spacing: -0.025em;
    }
    .text-heading-xl {
        font-size: 3rem;
        line-height: 1.2;
        font-weight: 700;
        letter-spacing: -0.025em;
    }
    .text-heading-lg {
        font-size: 2.25rem;
        line-height: 1.3;
        font-weight: 500;
        letter-spacing: -0.025em;
    }
    .text-heading-md {
        font-size: 1.875rem;
        line-height: 1.3;
        font-weight: 500;
    }
    .text-heading-sm {
        font-size: 1.5rem;
        line-height: 1.4;
        font-weight: 500;
    }

    /* Enhanced Body Text */
    .text-body-xl {
        font-size: 1.25rem;
        line-height: 1.7;
        font-weight: 400;
    }
    .text-body-lg {
        font-size: 1.125rem;
        line-height: 1.7;
        font-weight: 400;
    }
    .text-body {
        font-size: 1rem;
        line-height: 1.6;
        font-weight: 400;
    }
    .text-body-sm {
        font-size: 0.875rem;
        line-height: 1.5;
        font-weight: 400;
    }

    /* Modern Gradient Utilities */
    .bg-gradient-medical {
        background: linear-gradient(
            135deg,
            hsl(var(--medical-teal)) 0%,
            hsl(var(--medical-blue)) 100%
        );
    }
    .bg-gradient-primary {
        background: linear-gradient(
            135deg,
            hsl(var(--primary-500)) 0%,
            hsl(var(--primary-700)) 100%
        );
    }
    .bg-gradient-trust {
        background: linear-gradient(
            135deg,
            hsl(var(--trust-navy)) 0%,
            hsl(var(--primary-700)) 100%
        );
    }

    /* Enhanced Shadow System */
    .shadow-soft {
        box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07),
            0 10px 20px -2px rgba(0, 0, 0, 0.04);
    }
    .shadow-medium {
        box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1),
            0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .shadow-strong {
        box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15),
            0 2px 10px -2px rgba(0, 0, 0, 0.05);
    }

    /* Modern Transitions */
    .transition-smooth {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .transition-bounce {
        transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }
}

@layer base {
    :root {
        /* Enhanced Background & Foreground */
        --background: 0 0% 100%;
        --foreground: 210 40% 8%;

        /* Enhanced Card System */
        --card: 0 0% 100%;
        --card-foreground: 210 40% 8%;

        /* Enhanced Popover */
        --popover: 0 0% 100%;
        --popover-foreground: 210 40% 8%;

        /* Medical-themed Primary Colors */
        --primary: 214 84% 56%;
        --primary-foreground: 0 0% 98%;
        --primary-50: 240 249 255;
        --primary-100: 224 242 254;
        --primary-500: 214 84% 56%;
        --primary-600: 213 82% 51%;
        --primary-700: 212 78% 46%;

        /* Enhanced Secondary */
        --secondary: 210 40% 96%;
        --secondary-foreground: 210 40% 8%;

        /* Enhanced Muted */
        --muted: 210 40% 96%;
        --muted-foreground: 210 40% 45%;

        /* Enhanced Accent */
        --accent: 210 40% 96%;
        --accent-foreground: 210 40% 8%;

        /* Medical Theme Colors */
        --medical-blue: 214 84% 56%;
        --medical-teal: 173 80% 40%;
        --medical-green: 142 76% 36%;
        --trust-navy: 213 78% 26%;

        /* Enhanced Destructive */
        --destructive: 0 84% 60%;
        --destructive-foreground: 0 0% 98%;

        /* Enhanced Borders & Inputs */
        --border: 214 32% 91%;
        --input: 214 32% 91%;
        --ring: 214 84% 56%;

        /* Enhanced Chart Colors */
        --chart-1: 214 84% 56%;
        --chart-2: 173 80% 40%;
        --chart-3: 142 76% 36%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;

        /* Enhanced Radius */
        --radius: 0.75rem;
    }
    .dark {
        /* Enhanced Dark Background & Foreground */
        --background: 210 40% 4%;
        --foreground: 0 0% 98%;

        /* Enhanced Dark Card System */
        --card: 210 40% 4%;
        --card-foreground: 0 0% 98%;

        /* Enhanced Dark Popover */
        --popover: 210 40% 4%;
        --popover-foreground: 0 0% 98%;

        /* Medical-themed Dark Primary */
        --primary: 214 84% 66%;
        --primary-foreground: 210 40% 8%;

        /* Enhanced Dark Secondary */
        --secondary: 210 40% 15%;
        --secondary-foreground: 0 0% 98%;

        /* Enhanced Dark Muted */
        --muted: 210 40% 15%;
        --muted-foreground: 210 40% 64%;

        /* Enhanced Dark Accent */
        --accent: 210 40% 15%;
        --accent-foreground: 0 0% 98%;

        /* Enhanced Dark Destructive */
        --destructive: 0 63% 31%;
        --destructive-foreground: 0 0% 98%;

        /* Enhanced Dark Borders & Inputs */
        --border: 210 40% 15%;
        --input: 210 40% 15%;
        --ring: 214 84% 66%;

        /* Enhanced Dark Chart Colors */
        --chart-1: 214 84% 66%;
        --chart-2: 173 80% 50%;
        --chart-3: 142 76% 46%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}
